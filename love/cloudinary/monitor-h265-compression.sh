#!/bin/bash

# H.265视频压缩进度监控脚本
# 基于monitor-compression.sh升级，支持四层架构

echo "🎬 H.265视频压缩进度监控 (四层架构)"
echo "========================================"

# 获取所有源视频文件信息
declare -A original_sizes
declare -A original_names

if [ -d "src/client/assets/videos" ]; then
    for video_dir in src/client/assets/videos/*/; do
        if [ -d "$video_dir" ]; then
            video_name=$(basename "$video_dir")
            video_file="$video_dir$video_name.mp4"
            if [ -f "$video_file" ]; then
                size=$(stat -c%s "$video_file" 2>/dev/null || echo "0")
                size_mb=$(echo "scale=1; $size / 1024 / 1024" | bc 2>/dev/null || echo "0")
                original_sizes["$video_name"]="$size"
                original_names["$video_name"]="$size_mb"
            fi
        fi
    done
fi

echo "📊 源视频文件信息:"
for name in "${!original_names[@]}"; do
    printf "  %-15s %8s MB\n" "$name" "${original_names[$name]}"
done
echo ""

while true; do
    clear
    echo "🎬 H.265视频压缩进度监控 (四层架构)"
    echo "========================================"
    
    # 显示源文件信息
    echo "📊 源视频文件:"
    for name in "${!original_names[@]}"; do
        printf "  %-15s %8s MB\n" "$name" "${original_names[$name]}"
    done
    echo ""
    
    # 检查进程状态
    ffmpeg_count=$(ps aux | grep ffmpeg | grep libx265 | grep -v grep | wc -l)
    echo "🔄 活跃H.265压缩进程: $ffmpeg_count"
    
    if [ "$ffmpeg_count" -gt 0 ]; then
        echo "📹 当前压缩任务:"
        ps aux | grep ffmpeg | grep libx265 | grep -v grep | while read line; do
            echo "  $line" | awk '{print "  进程ID: " $2 " | " $11 " " $12 " " $13}'
        done
    fi
    echo ""
    
    # 显示压缩进度
    echo "📁 压缩进度 (video/compressed/):"
    if [ -d "video/compressed" ]; then
        for file in video/compressed/*.mp4; do
            if [ -f "$file" ]; then
                filename=$(basename "$file" .mp4)
                size=$(stat -c%s "$file" 2>/dev/null || echo "0")
                size_mb=$(echo "scale=1; $size / 1024 / 1024" | bc 2>/dev/null || echo "0")
                
                # 计算压缩率
                if [ "${original_sizes[$filename]}" ] && [ "${original_sizes[$filename]}" -gt 0 ]; then
                    original_size="${original_sizes[$filename]}"
                    compression_ratio=$(echo "scale=1; (1 - $size / $original_size) * 100" | bc 2>/dev/null || echo "0")
                    printf "  %-15s %8s MB (压缩率: %5s%%) ✅\n" "$filename" "$size_mb" "$compression_ratio"
                else
                    printf "  %-15s %8s MB (新文件) ✅\n" "$filename" "$size_mb"
                fi
            fi
        done
    else
        echo "  压缩目录尚未创建..."
    fi
    
    echo ""
    echo "📤 上传准备状态:"
    
    # R2上传准备
    echo "  🌐 R2上传目录 (video/r2-upload/):"
    if [ -d "video/r2-upload" ]; then
        r2_count=$(ls -1 video/r2-upload/*.mp4 2>/dev/null | wc -l)
        echo "    准备文件数: $r2_count"
        if [ "$r2_count" -gt 0 ]; then
            ls -1 video/r2-upload/*.mp4 2>/dev/null | while read file; do
                filename=$(basename "$file")
                size=$(stat -c%s "$file" 2>/dev/null || echo "0")
                size_mb=$(echo "scale=1; $size / 1024 / 1024" | bc 2>/dev/null || echo "0")
                printf "      %-15s %8s MB\n" "$filename" "$size_mb"
            done
        fi
    else
        echo "    目录未创建"
    fi
    
    # Cloudinary上传准备
    echo "  ☁️  Cloudinary上传目录 (video/cloudinary-upload/):"
    if [ -d "video/cloudinary-upload" ]; then
        cloudinary_count=$(ls -1 video/cloudinary-upload/*.mp4 2>/dev/null | wc -l)
        echo "    准备文件数: $cloudinary_count"
        if [ "$cloudinary_count" -gt 0 ]; then
            ls -1 video/cloudinary-upload/*.mp4 2>/dev/null | while read file; do
                filename=$(basename "$file")
                size=$(stat -c%s "$file" 2>/dev/null || echo "0")
                size_mb=$(echo "scale=1; $size / 1024 / 1024" | bc 2>/dev/null || echo "0")
                printf "      %-15s %8s MB\n" "$filename" "$size_mb"
            done
        fi
    else
        echo "    目录未创建"
    fi
    
    echo ""
    echo "⏱️  $(date '+%H:%M:%S') - 每5秒更新一次 (Ctrl+C 退出)"
    
    # 如果没有活跃进程，显示完成信息
    if [ "$ffmpeg_count" -eq 0 ]; then
        echo ""
        echo "✅ 所有H.265压缩任务已完成！"
        echo ""
        echo "🎯 H.265压缩总结："
        echo "  • 编码器: libx265 (H.265/HEVC)"
        echo "  • 质量设置: CRF 14/16"
        echo "  • 预设: slow (高质量)"
        echo "  • 音频: 无音频处理"
        echo "  • 智能策略: 小文件直接复制"
        echo ""
        echo "📁 文件位置:"
        echo "  • 本地压缩: video/compressed/"
        echo "  • R2上传: video/r2-upload/"
        echo "  • Cloudinary上传: video/cloudinary-upload/"
        echo ""
        echo "🚀 可以开始上传到四层CDN架构了！"
        break
    fi
    
    sleep 5
done
