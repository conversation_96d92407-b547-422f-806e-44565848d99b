#!/bin/bash

# H.265高效视频压缩脚本 (四层架构优化版)
# 使用H.265编码器，CRF 14/16，无音频处理，智能压缩策略
# 基于现有compress-videos-for-cloudinary.sh升级

echo "🎬 开始H.265高效视频压缩 (四层架构优化)..."

# 检查ffmpeg H.265支持
if ! ffmpeg -encoders 2>/dev/null | grep -q libx265; then
    echo "❌ 错误: FFmpeg不支持libx265编码器"
    echo "请安装支持H.265的FFmpeg版本"
    exit 1
fi

# 创建输出目录
mkdir -p video/compressed
mkdir -p video/r2-upload
mkdir -p video/cloudinary-upload

echo "📁 输出目录："
echo "  - 本地压缩: video/compressed/"
echo "  - R2上传准备: video/r2-upload/"
echo "  - Cloudinary上传准备: video/cloudinary-upload/"

# 智能压缩策略函数
compress_video() {
    local input_file="$1"
    local output_name="$2"
    local crf_value="$3"
    local description="$4"
    
    if [ ! -f "$input_file" ]; then
        echo "⚠️  跳过 $output_name: 源文件不存在 ($input_file)"
        return 1
    fi
    
    # 获取文件大小 (MB)
    local file_size=$(stat -c%s "$input_file" 2>/dev/null || echo "0")
    local file_size_mb=$(echo "scale=1; $file_size / 1024 / 1024" | bc 2>/dev/null || echo "0")
    
    echo "📹 处理 $output_name ($description)..."
    echo "   源文件大小: ${file_size_mb}MB"
    
    # 智能压缩策略：小于20MB的文件直接复制
    if (( $(echo "$file_size_mb < 20" | bc -l) )); then
        echo "   📋 文件较小，直接复制到输出目录"
        cp "$input_file" "video/compressed/$output_name"
        cp "$input_file" "video/r2-upload/$output_name"
        cp "$input_file" "video/cloudinary-upload/$output_name"
        echo "   ✅ 复制完成"
        return 0
    fi
    
    # H.265压缩
    echo "   🔄 开始H.265压缩 (CRF $crf_value)..."
    
    ffmpeg -i "$input_file" \
        -c:v libx265 \
        -crf "$crf_value" \
        -preset slow \
        -an \
        -pix_fmt yuv420p \
        -movflags +faststart \
        -tag:v hvc1 \
        -y \
        "video/compressed/$output_name"
    
    if [ $? -eq 0 ]; then
        # 获取压缩后大小
        local compressed_size=$(stat -c%s "video/compressed/$output_name" 2>/dev/null || echo "0")
        local compressed_mb=$(echo "scale=1; $compressed_size / 1024 / 1024" | bc 2>/dev/null || echo "0")
        local compression_ratio=$(echo "scale=1; (1 - $compressed_size / $file_size) * 100" | bc 2>/dev/null || echo "0")
        
        echo "   📊 压缩完成: ${compressed_mb}MB (压缩率: ${compression_ratio}%)"
        
        # 复制到上传目录
        cp "video/compressed/$output_name" "video/r2-upload/"
        cp "video/compressed/$output_name" "video/cloudinary-upload/"
        echo "   ✅ 已复制到上传目录"
        return 0
    else
        echo "   ❌ 压缩失败"
        return 1
    fi
}

# 压缩所有视频文件
echo ""
echo "🚀 开始批量压缩..."

# Anniversary视频 - CRF 14 (最高质量)
compress_video "src/client/assets/videos/anniversary/anniversary.mp4" "anniversary.mp4" "14" "纪念日视频 - 最高质量"

# Together-days视频 - CRF 16 (配置指定)
compress_video "src/client/assets/videos/together-days/together-days.mp4" "together-days.mp4" "16" "在一起的日子 - 平衡质量"

# Memorial视频 - CRF 14 (高质量)
compress_video "src/client/assets/videos/memorial/memorial.mp4" "memorial.mp4" "14" "纪念相册 - 高质量"

# Home视频 - CRF 14 (高质量)
compress_video "src/client/assets/videos/home/<USER>" "home.mp4" "14" "首页视频 - 高质量"

# Meetings视频 - CRF 14 (高质量)
compress_video "src/client/assets/videos/meetings/meetings.mp4" "meetings.mp4" "14" "相遇回忆 - 高质量"

# 压缩结果统计
echo ""
echo "📊 H.265压缩结果汇总："
echo "========================================"

echo ""
echo "原始文件大小："
if [ -d "src/client/assets/videos" ]; then
    find src/client/assets/videos -name "*.mp4" -exec ls -lh {} \; | sort
else
    echo "  源文件目录不存在"
fi

echo ""
echo "压缩后文件大小："
if [ -d "video/compressed" ]; then
    ls -lh video/compressed/*.mp4 2>/dev/null | sort
else
    echo "  压缩文件目录为空"
fi

echo ""
echo "R2上传准备文件："
if [ -d "video/r2-upload" ]; then
    ls -lh video/r2-upload/*.mp4 2>/dev/null | sort
else
    echo "  R2上传目录为空"
fi

echo ""
echo "Cloudinary上传准备文件："
if [ -d "video/cloudinary-upload" ]; then
    ls -lh video/cloudinary-upload/*.mp4 2>/dev/null | sort
else
    echo "  Cloudinary上传目录为空"
fi

# 计算总体压缩效果
echo ""
echo "🎯 H.265压缩参数总结："
echo "  • 编码器: libx265 (H.265/HEVC)"
echo "  • 质量: CRF 14 (默认) / CRF 16 (together-days)"
echo "  • 预设: slow (高质量压缩)"
echo "  • 音频: 无音频 (-an)"
echo "  • 像素格式: yuv420p (兼容性)"
echo "  • 智能策略: <20MB文件直接复制"

echo ""
echo "✅ H.265压缩任务完成！"
echo "📁 本地压缩: video/compressed/"
echo "🌐 R2上传准备: video/r2-upload/"
echo "☁️  Cloudinary上传准备: video/cloudinary-upload/"
echo "🚀 所有文件已准备好上传到四层CDN架构"
